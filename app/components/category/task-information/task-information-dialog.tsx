import CustomTabs from '@/components/common/custom-tabs';
import DraggableInputBoxPanel from '@/components/common/draggable/draggable-inputbox-panel';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogTrigger
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import TaskInformationPanel from './task-information-panel';
import { useFieldArray, type Control, type FieldArrayPath, type Path } from 'react-hook-form';
import type { CategorySchemaProps } from '@/lib/schema/category-schema';
import FormInput from '@/components/common/form-input';
import { FormControl, FormField, FormItem } from '@/components/ui/form';

const languages = [
  { label: 'English', value: 'en' },
  { label: 'Khmer', value: 'km' },
  { label: 'Vietnamese', value: 'vn' },
  { label: 'Chinese (Traditional)', value: 'zh' },
  { label: 'Chinese (Simplified)', value: 'cn' }
];

type Props = {
  control: Control<CategorySchemaProps>;
  name: Path<CategorySchemaProps>;
  title: string;
};

export function TaskInformationDialog({ control, name, title }: Props) {
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();
  const [products, setProducts] = useState<DraggableInputProps[]>([]);

  const { fields, append, remove, move } = useFieldArray({
    control,
    name: name as 'taskInformation' // Cast to the specific field name
  });
  const index = fields.length - 1;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <TaskInformationPanel
          title={title}
          control={control}
          name={name}
          onClick={() => setOpen(true)}
        />
      </DialogTrigger>
      <DialogContent className="p-0 w-[75vw] flex flex-col h-[70vh] overflow-hidden gap-0">
        <DialogHeader className="p-6  ">
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <CustomTabs tabs={languages}>
          {(tab) => (
            <div>
              <FormField
                control={control}
                name={`${name}.${index}.value.${tab.value}.title` as Path<CategorySchemaProps>}
                render={({ field }) => (
                  <FormItem className="w-full">
                    <Input
                      className="flex flex-1"
                      onChange={field.onChange}
                      value={typeof field.value === 'string' ? field.value : ''}
                    />
                  </FormItem>
                )}
              />
              <DraggableInputBoxPanel
                // title={t('categoryPage.product')}
                buttonText={t('categoryPage.addProduct')}
                control={control}
                name={name}
                index={index}
                lang={tab.value}
              />
            </div>
          )}
        </CustomTabs>

        <DialogFooter className="border-t p-6">
          <Button size="sm" type="submit">
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
