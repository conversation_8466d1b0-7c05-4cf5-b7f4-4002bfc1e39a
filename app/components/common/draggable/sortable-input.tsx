import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Grip, Minus } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { FormField, FormItem } from '@/components/ui/form';
import type { Control, FieldValues, Path } from 'react-hook-form';

type Props<T extends FieldValues> = {
  control: Control<T>;
  name: Path<T>;
  index: number;
  parentIndex: number;
  lang: string;
  placeholder: string;
  onRemove: () => void;
};

export default function SortableInput<T extends FieldValues>({
  control,
  name,
  index,
  lang,
  parentIndex,
  placeholder,
  onRemove
}: Props<T>) {
  const fieldId = `${name}.${parentIndex}.value.${lang}.${index}.id`;
  const fieldName = `${name}.${parentIndex}.value.${lang}.${index}.value`;
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: fieldId
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-center justify-between px-1 group hover:bg-gray-50">
      <div className="flex items-center">
        <button
          className="cursor-grab active:cursor-grabbing p-1 text-gray-400 hover:text-gray-600"
          {...attributes}
          {...listeners}>
          <Grip className="h-4 w-4" />
        </button>
      </div>
      <div className="flex-1 flex px-4">
        {/* This component is now fully integrated with RHF */}
        <FormField
          control={control}
          name={fieldName as Path<T>}
          render={({ field }) => (
            <FormItem className="w-full">
              <Input placeholder={placeholder} value={field.value} onChange={field.onChange} />
            </FormItem>
          )}
        />
      </div>

      <button
        onClick={onRemove}
        className="p-1 text-red-400 hover:text-red-600 opacity-0 group-hover:opacity-100 transition-opacity">
        <Minus className="h-4 w-4" />
      </button>
    </div>
  );
}
