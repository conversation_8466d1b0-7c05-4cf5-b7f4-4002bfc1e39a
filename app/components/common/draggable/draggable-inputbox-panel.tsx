import type { Dispatch, SetStateAction } from 'react';
import { useTranslation } from 'react-i18next';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import clsx from 'clsx';
import SortableInput from './sortable-input';
import {
  useFieldArray,
  type Control,
  type FieldArrayPath,
  type FieldValues,
  type Path
} from 'react-hook-form';
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';

type Props<T extends FieldValues> = {
  buttonText?: string;
  control: Control<T>;
  name: Path<T>;
  index: number;
  lang: string;
};

export default function DraggableInputBoxPanel<T extends FieldValues>({
  buttonText,
  control,
  name,
  index,
  lang
}: Props<T>) {
  const { t } = useTranslation();

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  );

  const parentIndex = index;

  const fieldName = `${name}.${parentIndex}.value.${lang}.description` as FieldArrayPath<T>;

  const { fields, append, remove, move } = useFieldArray({
    control,
    name: fieldName
  });
  // --- EVENT HANDLERS NOW USE RHF METHODS ---
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (over && active.id !== over.id) {
      const oldIndex = fields.findIndex((item) => item.id === active.id);
      const newIndex = fields.findIndex((item) => item.id === over.id);
      move(oldIndex, newIndex); // Use the 'move' function from useFieldArray
    }
  };

  // const handleUpdateTask = (id: string, updates: Partial<DraggableInputProps>) => {
  //   onChange((prev) => prev.map((task) => (task.id === id ? { ...task, ...updates } : task)));
  // };

  // const handleDeleteTask = (id: string) => {
  //   onChange((prev) => prev.filter((task) => task.id !== id));
  // };

  const handleAddTask = () => {
    // Use the 'append' function. The object must match your schema.
    // crypto.randomUUID() is great for generating unique IDs without hydration errors.
    append({
      id: crypto.randomUUID(),
      value: ''
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any);
  };

  // return (
  //   <div>
  //     {fields.map((field, index) => (
  //       <div key={index}>{field.value}</div>
  //     ))}
  //   </div>
  // );

  return (
    <Card className="flex flex-col gap-0 shadow-none">
      <CardContent className="p-0">
        <div
          className={clsx({
            'pb-4': fields.length > 0
          })}>
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}>
            <SortableContext
              items={fields.map((field) => field.id)}
              strategy={verticalListSortingStrategy}>
              <div className="space-y-4">
                {fields.map((field, index) => (
                  <SortableInput
                    key={field.id} // RHF provides a stable key
                    control={control}
                    parentIndex={parentIndex}
                    name={name}
                    index={index}
                    placeholder={lang}
                    onRemove={() => remove(index)} // Pass the 'remove' function
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        </div>
      </CardContent>
      <div>
        <Button className="!pl-0" variant="link" onClick={handleAddTask}>
          <Plus />
          {buttonText || t('addAnother')}
        </Button>
      </div>
    </Card>
  );
}
