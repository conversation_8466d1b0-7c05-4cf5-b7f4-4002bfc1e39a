import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import CustomHeader from '@/components/headers/custom-header';
import { Form, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import ProfilePicker from '@/components/common/profile-picker';
import CustomSelect from '@/components/common/custom-select';
import DragDropFileUpload from '@/components/common/drag-drop-file-upload';
import { categorySchema, type CategorySchemaProps } from '@/lib/schema/category-schema';
import { useState } from 'react';
import DraggableComboboxPanel from '@/components/common/draggable/draggable-combobox-panel';
import ContentWrapper from '@/components/common/content-wrapper';
import { useTranslation } from 'react-i18next';
import FormInputMultipleLanguages from '@/components/common/form-input-multiple-languages';
import { TaskInformationDialog } from '@/components/category/task-information/task-information-dialog';

export default function NewCategory() {
  const { t } = useTranslation();
  const [products, setProducts] = useState<DraggableComboBoxProps[]>([]);
  const [categoryAddOn, setCategoryAddOn] = useState<DraggableComboBoxProps[]>([]);

  const form = useForm<CategorySchemaProps>({
    mode: 'onSubmit',
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: {
        en: '',
        km: '',
        vn: '',
        zh: '',
        cn: ''
      },
      status: 'Active',
      attachments: [],
      taskInformation: [
        {
          id: '0',
          value: {
            en: {
              id: '0',
              title: 'en',
              description: [
                {
                  id: '10',
                  value: 'description 123'
                }
              ]
            },
            km: {
              id: '0',
              title: 'ខ្មែរ',
              description: []
            },
            cn: {
              id: '0',
              title: 'cn',
              description: []
            },
            vn: {
              id: '0',
              title: 'vn',
              description: []
            },
            zh: {
              id: '0',
              title: 'zn',
              description: []
            }
          }
        }
      ]
    }
  });
  const onSubmit = async (values: CategorySchemaProps) => {
    console.log('Form submitted with values:', values);
  };

  return (
    <div>
      <CustomHeader onSave={form.handleSubmit(onSubmit)} />
      <ContentWrapper>
        <div className="p-6 flex flex-row gap-6 items-baseline">
          <Card className="flex flex-1">
            <CardHeader className="gap-4">
              <CardTitle>{t('categoryPage.details')}</CardTitle>
              <ProfilePicker />
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)}>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-3">
                    <FormInputMultipleLanguages
                      form={form}
                      name="name"
                      label="Name"
                      placeholder="Name"
                    />

                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Status</FormLabel>
                          <CustomSelect
                            className="!h-10"
                            placeholder="Status"
                            data={[
                              { label: 'Active', value: 'Active' },
                              { label: 'Resigned', value: 'Resigned' }
                            ]}
                            value={field.value}
                            onValueChange={field.onChange}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="mt-4">
                    <DragDropFileUpload placeholder="What's Included" />
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
          <div className="w-[368px] flex flex-col gap-4 h-full">
            <TaskInformationDialog
              control={form.control}
              name="taskInformation"
              title={t('categoryPage.taskInformation')}
            />
            {/* <DraggableInputPanel
              control={form.control}
              name="taskInformation"
              title={t('categoryPage.taskInformation')}
              data={tasks}
              onChange={setTasks}
            /> */}
            <DraggableComboboxPanel
              title={t('categoryPage.product')}
              buttonText={t('categoryPage.addProduct')}
              data={products}
              onChange={setProducts}
            />
            <DraggableComboboxPanel
              title={t('categoryPage.categoryAddOn')}
              buttonText={t('categoryPage.addCategoryAddOn')}
              data={categoryAddOn}
              onChange={setCategoryAddOn}
            />
          </div>
        </div>
      </ContentWrapper>
    </div>
  );
}
